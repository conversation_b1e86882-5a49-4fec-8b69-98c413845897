#!/usr/bin/env python3
"""
ESP32 Device Scanner Listener

A Python HTTP server utility that continuously listens for incoming HTTP POST requests
containing ESP32 device scan data and displays them in a formatted console output.

Usage:
    python json_listener.py [--port PORT] [--host HOST]

Example:
    python json_listener.py                           # Listen on ************:8080
    python json_listener.py --port 9000               # Custom port
    python json_listener.py --host 0.0.0.0 --port 8080  # Listen on all interfaces
"""

import json
import argparse
import signal
import sys
from datetime import datetime
from http.server import HTTPServer, BaseHTTPRequestHandler
from urllib.parse import urlparse


class JSONRequestHandler(BaseHTTPRequestHandler):
    """HTTP request handler for processing JSON data."""
    
    def log_message(self, format, *args):
        """Override default logging to customize output format."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"[{timestamp}] {format % args}")
    
    def do_POST(self):
        """Handle POST requests containing JSON data from ESP32 scanning device."""
        try:
            # Get client information
            client_ip = self.client_address[0]
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # Get content length
            content_length = int(self.headers.get('Content-Length', 0))

            if content_length == 0:
                self.send_error_response(400, "No data received")
                print(f"[{timestamp}] ❌ ERROR: No data in request body from {client_ip}")
                return

            # Read the request body
            post_data = self.rfile.read(content_length)

            try:
                # Decode the data
                data_str = post_data.decode('utf-8')

                # Parse JSON
                json_data = json.loads(data_str)

                # Display ESP32 scan data in a specialized format
                self.display_esp32_scan_data(json_data, client_ip, timestamp)

                # Send success response
                self.send_success_response(json_data)

            except json.JSONDecodeError as e:
                error_msg = f"Invalid JSON format: {str(e)}"
                self.send_error_response(400, error_msg)
                print(f"[{timestamp}] ❌ JSON Parse Error from {client_ip}: {error_msg}")
                print(f"Raw data: {data_str[:200]}{'...' if len(data_str) > 200 else ''}")

            except UnicodeDecodeError as e:
                error_msg = f"Unable to decode data as UTF-8: {str(e)}"
                self.send_error_response(400, error_msg)
                print(f"[{timestamp}] ❌ Encoding Error from {client_ip}: {error_msg}")

        except Exception as e:
            error_msg = f"Server error: {str(e)}"
            self.send_error_response(500, error_msg)
            print(f"[{timestamp}] ❌ Server Error: {error_msg}")

    def display_esp32_scan_data(self, data, client_ip, timestamp):
        """Display ESP32 scan data in a specialized format."""
        print(f"\n{'='*80}")
        print(f"🔍 ESP32 SCAN DATA RECEIVED")
        print(f"📡 From: {client_ip} | ⏰ Time: {timestamp}")
        print(f"{'='*80}")

        # Check if this looks like ESP32 scan data
        if isinstance(data, dict):
            # Handle different possible data structures
            if 'devices' in data or 'esp32_devices' in data or 'scan_results' in data:
                self.display_device_list(data)
            elif 'mac' in data or 'ssid' in data or 'rssi' in data:
                self.display_single_device(data)
            elif any(key in data for key in ['scanner_id', 'scan_time', 'location']):
                self.display_scan_session(data)
            else:
                self.display_generic_data(data)
        elif isinstance(data, list):
            self.display_device_array(data)
        else:
            print(f"📦 Raw Data: {json.dumps(data, indent=2)}")

        print(f"{'='*80}\n")

    def display_device_list(self, data):
        """Display a list of ESP32 devices."""
        devices_key = None
        for key in ['devices', 'esp32_devices', 'scan_results', 'found_devices']:
            if key in data:
                devices_key = key
                break

        if devices_key and isinstance(data[devices_key], list):
            devices = data[devices_key]
            print(f"📱 Found {len(devices)} ESP32 Device(s):")
            print(f"┌{'─'*78}┐")

            for i, device in enumerate(devices, 1):
                self.print_device_info(device, i)

            print(f"└{'─'*78}┘")

            # Show additional metadata if present
            metadata_keys = [k for k in data.keys() if k != devices_key]
            if metadata_keys:
                print(f"\n📋 Scan Metadata:")
                for key in metadata_keys:
                    print(f"   {key}: {data[key]}")
        else:
            self.display_generic_data(data)

    def display_device_array(self, devices):
        """Display an array of devices."""
        print(f"📱 Found {len(devices)} ESP32 Device(s):")
        print(f"┌{'─'*78}┐")

        for i, device in enumerate(devices, 1):
            self.print_device_info(device, i)

        print(f"└{'─'*78}┘")

    def display_single_device(self, device):
        """Display a single ESP32 device."""
        print(f"📱 Single ESP32 Device Found:")
        print(f"┌{'─'*78}┐")
        self.print_device_info(device, 1)
        print(f"└{'─'*78}┘")

    def print_device_info(self, device, index):
        """Print formatted information for a single device."""
        print(f"│ Device #{index:<2} {'─'*65} │")

        # Common ESP32 fields
        fields_map = {
            'mac': '🔗 MAC Address',
            'mac_address': '🔗 MAC Address',
            'ssid': '📶 SSID',
            'network_name': '📶 Network Name',
            'rssi': '📡 Signal Strength (RSSI)',
            'signal_strength': '📡 Signal Strength',
            'channel': '📻 Channel',
            'encryption': '🔒 Encryption',
            'security': '🔒 Security',
            'ip': '🌐 IP Address',
            'ip_address': '🌐 IP Address',
            'hostname': '💻 Hostname',
            'device_name': '📱 Device Name',
            'vendor': '🏭 Vendor',
            'manufacturer': '🏭 Manufacturer',
            'distance': '📏 Distance',
            'last_seen': '👁️ Last Seen',
            'first_seen': '🆕 First Seen',
            'status': '⚡ Status'
        }

        # Display known fields
        for key, label in fields_map.items():
            if key in device:
                value = device[key]
                if key == 'rssi' or key == 'signal_strength':
                    # Add signal strength indicator
                    if isinstance(value, (int, float)):
                        if value > -50:
                            indicator = "🟢 Excellent"
                        elif value > -60:
                            indicator = "🟡 Good"
                        elif value > -70:
                            indicator = "🟠 Fair"
                        else:
                            indicator = "🔴 Weak"
                        print(f"│   {label}: {value} dBm ({indicator})")
                    else:
                        print(f"│   {label}: {value}")
                else:
                    print(f"│   {label}: {value}")

        # Display any additional fields
        displayed_keys = set(fields_map.keys())
        additional_keys = [k for k in device.keys() if k not in displayed_keys]
        if additional_keys:
            for key in additional_keys:
                print(f"│   📄 {key}: {device[key]}")

        if index > 1:  # Add separator between devices
            print(f"│ {'─'*76} │")

    def display_scan_session(self, data):
        """Display scan session information."""
        print(f"🔍 Scan Session Information:")

        session_fields = {
            'scanner_id': '🆔 Scanner ID',
            'scan_time': '⏰ Scan Time',
            'scan_duration': '⏱️ Duration',
            'location': '📍 Location',
            'scan_type': '🔍 Scan Type',
            'total_devices': '📊 Total Devices',
            'new_devices': '🆕 New Devices'
        }

        for key, label in session_fields.items():
            if key in data:
                print(f"   {label}: {data[key]}")

        # Show remaining data
        remaining_keys = [k for k in data.keys() if k not in session_fields]
        if remaining_keys:
            print(f"\n📦 Additional Data:")
            for key in remaining_keys:
                if isinstance(data[key], (dict, list)):
                    print(f"   {key}: {json.dumps(data[key], indent=4)}")
                else:
                    print(f"   {key}: {data[key]}")

    def display_generic_data(self, data):
        """Display generic JSON data with nice formatting."""
        print(f"📦 JSON Data:")
        print(json.dumps(data, indent=2, ensure_ascii=False))
    
    def do_GET(self):
        """Handle GET requests with server status."""
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
        client_ip = self.client_address[0]
        
        print(f"[{timestamp}] GET request from {client_ip} - Path: {self.path}")
        
        if self.path == '/status' or self.path == '/':
            status_info = {
                "status": "running",
                "server": "JSON HTTP Listener",
                "timestamp": timestamp,
                "message": "Server is running and ready to receive JSON data via POST requests"
            }
            
            self.send_response(200)
            self.send_header('Content-Type', 'application/json')
            self.send_header('Access-Control-Allow-Origin', '*')
            self.end_headers()
            
            response_data = json.dumps(status_info, indent=2)
            self.wfile.write(response_data.encode('utf-8'))
            
            print(f"[{timestamp}] ✅ Sent status response to {client_ip}")
        else:
            self.send_error_response(404, "Endpoint not found. Use POST to send JSON data or GET /status for server status.")
    
    def do_OPTIONS(self):
        """Handle OPTIONS requests for CORS preflight."""
        self.send_response(200)
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'POST, GET, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
    
    def send_success_response(self, received_data):
        """Send a success response back to the client."""
        response_data = {
            "status": "success",
            "message": "JSON data received successfully",
            "timestamp": datetime.now().isoformat(),
            "received_data": received_data
        }
        
        self.send_response(200)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(response_data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))
    
    def send_error_response(self, status_code, error_message):
        """Send an error response back to the client."""
        response_data = {
            "status": "error",
            "message": error_message,
            "timestamp": datetime.now().isoformat()
        }
        
        self.send_response(status_code)
        self.send_header('Content-Type', 'application/json')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.end_headers()
        
        response_json = json.dumps(response_data, indent=2)
        self.wfile.write(response_json.encode('utf-8'))


class JSONListenerServer:
    """Main server class for the JSON HTTP Listener."""
    
    def __init__(self, host='localhost', port=8080):
        self.host = host
        self.port = port
        self.server = None
    
    def start(self):
        """Start the HTTP server."""
        try:
            self.server = HTTPServer((self.host, self.port), JSONRequestHandler)
            
            print(f"🚀 JSON HTTP Listener Server Starting...")
            print(f"📡 Listening on {self.host}:{self.port}")
            print(f"🔗 Server URL: http://{self.host}:{self.port}")
            print(f"📋 Status endpoint: http://{self.host}:{self.port}/status")
            print(f"📝 Send POST requests with JSON data to receive and display them")
            print(f"⏹️  Press Ctrl+C to stop the server")
            print(f"{'='*60}\n")
            
            # Start serving requests
            self.server.serve_forever()
            
        except OSError as e:
            if e.errno == 98 or "Address already in use" in str(e):
                print(f"❌ Error: Port {self.port} is already in use.")
                print(f"💡 Try using a different port with --port option")
            else:
                print(f"❌ Error starting server: {e}")
            sys.exit(1)
        except KeyboardInterrupt:
            self.stop()
        except Exception as e:
            print(f"❌ Unexpected error: {e}")
            sys.exit(1)
    
    def stop(self):
        """Stop the HTTP server gracefully."""
        if self.server:
            print(f"\n🛑 Shutting down server...")
            self.server.shutdown()
            self.server.server_close()
            print(f"✅ Server stopped successfully")


def signal_handler(signum, frame):
    """Handle interrupt signals gracefully."""
    print(f"\n🛑 Received interrupt signal. Shutting down...")
    sys.exit(0)


def main():
    """Main function to parse arguments and start the server."""
    parser = argparse.ArgumentParser(
        description="ESP32 Device Scanner Listener - Receives and displays ESP32 scan data via HTTP POST requests",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python json_listener.py                    # Start on ************:8080
  python json_listener.py --port 9000        # Start on ************:9000
  python json_listener.py --host 0.0.0.0     # Listen on all interfaces
  python json_listener.py --host ************* --port 8888  # Custom host and port

Test the server:
  curl -X POST http://************:8080 -H "Content-Type: application/json" -d '{"devices": [{"mac": "AA:BB:CC:DD:EE:FF", "rssi": -45}]}'
  curl http://************:8080/status
        """
    )
    
    parser.add_argument(
        '--host',
        default='************',
        help='Host to bind the server to (default: ************)'
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8080,
        help='Port to listen on (default: 8080)'
    )
    
    args = parser.parse_args()
    
    # Set up signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    # Create and start the server
    server = JSONListenerServer(host=args.host, port=args.port)
    server.start()


if __name__ == '__main__':
    main()
